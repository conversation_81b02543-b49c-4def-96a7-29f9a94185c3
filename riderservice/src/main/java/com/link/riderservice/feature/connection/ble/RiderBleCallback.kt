package com.link.riderservice.feature.connection.ble

import android.bluetooth.BluetoothDevice
import com.link.riderservice.libs.ble.data.Data

interface RiderBleCallback {
    fun onDataReceived(bluetoothDevice: BluetoothDevice, receivedData: Data)
    fun onDeviceConnecting(bluetoothDevice: BluetoothDevice)
    fun onDeviceConnected(bluetoothDevice: BluetoothDevice)
    fun onDeviceFailedToConnect(bluetoothDevice: BluetoothDevice, reason: Int)
    fun onDeviceReady(bluetoothDevice: BluetoothDevice)
    fun onDeviceDisconnecting(bluetoothDevice: BluetoothDevice)
    fun onDeviceDisconnected(bluetoothDevice: BluetoothDevice, connectionFailureReason: Int)
    fun onScanResult(discoveredBleDevices: List<BleDevice>)
    fun onRequestBt()
    fun onScanning()
    fun onScanFinish()
    fun onNeedBluetoothScanPermission()
    fun onNeedLocationPermission()
    fun onEnableNotificationFailed(bluetoothDevice: BluetoothDevice, status: Int)
}