package com.link.riderservice.feature.connection.coordinator

import android.annotation.SuppressLint
import android.bluetooth.BluetoothDevice
import android.content.Context
import android.media.projection.MediaProjection
import android.net.wifi.ScanResult
import android.view.Display
import com.link.riderservice.R
import com.link.riderservice.api.RiderService
import com.link.riderservice.api.callback.RiderServiceCallback
import com.link.riderservice.api.dto.BleStatus
import com.link.riderservice.api.dto.Connection
import com.link.riderservice.api.dto.NaviMode
import com.link.riderservice.api.dto.RiderServiceConfig
import com.link.riderservice.api.dto.WifiStatus
import com.link.riderservice.core.di.ModuleInject
import com.link.riderservice.core.extensions.coroutines.mainScope
import com.link.riderservice.core.extensions.flow.collectWithScope
import com.link.riderservice.core.extensions.flow.setState
import com.link.riderservice.core.utils.logging.logD
import com.link.riderservice.core.utils.logging.logE
import com.link.riderservice.core.utils.logging.logI
import com.link.riderservice.core.utils.logging.logW
import com.link.riderservice.core.utils.system.Platform
import com.link.riderservice.data.authorization.data.repository.AuthorizeRepositoryImpl
import com.link.riderservice.data.authorization.data.source.remote.AuthorizeRemoteSourceImpl
import com.link.riderservice.data.source.local.ConfigPreferences
import com.link.riderservice.feature.analytics.ConnectionAnalytics
import com.link.riderservice.feature.analytics.ConnectionTimeTracker
import com.link.riderservice.feature.analytics.logConnectionD
import com.link.riderservice.feature.analytics.logTcpConnection
import com.link.riderservice.feature.analytics.logWifiConnection
import com.link.riderservice.feature.connection.ble.BleDevice
import com.link.riderservice.feature.connection.ble.RiderBleCallback
import com.link.riderservice.feature.connection.ble.RiderBleManager
import com.link.riderservice.feature.connection.mode.WifiConnectionMode
import com.link.riderservice.feature.connection.recovery.ConnectionRecoveryManager
import com.link.riderservice.feature.connection.wifi.WiFiClientManager
import com.link.riderservice.feature.connection.wifi.WiFiClientManagerListener
import com.link.riderservice.feature.display.callback.DisplayNaviCallback
import com.link.riderservice.feature.display.manager.DisplayNaviManager
import com.link.riderservice.feature.messaging.toByteArray
import com.link.riderservice.libs.ble.data.Data
import com.link.riderservice.protobuf.RiderProtocol
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.retry
import kotlinx.coroutines.launch
import java.lang.ref.WeakReference
import java.util.Timer
import java.util.TimerTask

internal class ConnectionManager {
    private val mutableConnectionStatus = MutableStateFlow(Connection())
    private val connectionStatus = mutableConnectionStatus.asStateFlow()
    private var displayNaviManager: DisplayNaviManager = DisplayNaviManager(
        requestWifiInfo = {
            requestWifiInfo()
        },
        onWifiState = { opened ->
            if (!opened && connectionStatus.value.btStatus is BleStatus.DeviceConnected) {
                serviceCallbacks.forEach {
                    it.get()?.onDialogShow(
                        application.getString(R.string.error_title),
                        application.getString(R.string.open_wifi)
                    )
                }
            }
            serviceCallbacks.forEach {
                it.get()?.onWifiState(opened)
            }
        },
        onP2pWifiConnected = {
            ConnectionTimeTracker.recordWifiConnected()
        },
        onTcpConnecting = {
            ConnectionTimeTracker.recordTcpConnecting()
        }
    )
    private var connectionTimer: Timer? = null
    private var isManualDisconnect: Boolean = false
    private val authorizeRepository = AuthorizeRepositoryImpl(AuthorizeRemoteSourceImpl())
    private val serviceCallbacks: MutableList<WeakReference<RiderServiceCallback>> = mutableListOf()
    private var bleAddress: String? = null
    private var bleName: String? = null
    private val application by lazy { RiderService.Companion.instance.getApplication() }
    private var tempApSsid: String? = null
    private var tempApPassword: String? = null

    // Candidate credentials to be saved upon full connection success
    private var candidateApSsid: String? = null
    private var candidateApPassword: String? = null
    // For P2P, we might need to fetch details from SoftP2pManager upon connection.


    @Synchronized
    fun addCallback(callback: RiderServiceCallback) {
        serviceCallbacks.add(WeakReference(callback))
    }

    @Synchronized
    fun removeCallback(callback: RiderServiceCallback) {
        serviceCallbacks.removeIf { it.get() == callback }
    }

    private val bleCallback = object : RiderBleCallback {
        override fun onDataReceived(device: BluetoothDevice, data: Data) {
            data.value?.let { buffer ->
                ModuleInject.messageManager.enqueueIncoming(buffer, buffer.size)
            }
        }

        override fun onDeviceConnecting(device: BluetoothDevice) {
            logD(TAG, "onDeviceConnecting: $device ")
            mutableConnectionStatus.setState { copy(btStatus = BleStatus.DeviceConnecting(device)) }

            // 开始连接时间跟踪
            ConnectionTimeTracker.startConnection(device.address)
        }

        @SuppressLint("MissingPermission")
        override fun onDeviceConnected(device: BluetoothDevice) {
            logD(TAG, "onDeviceConnected")
            bleAddress = device.address
            bleName = device.name
            mutableConnectionStatus.setState { copy(btStatus = BleStatus.DeviceConnected(device)) }

            // 记录BLE连接成功
            ConnectionTimeTracker.recordBleConnected()
        }

        override fun onDeviceFailedToConnect(device: BluetoothDevice, reason: Int) {
            logD(TAG, "onDeviceFailedToConnect")
            mutableConnectionStatus.setState {
                copy(btStatus = BleStatus.DeviceFailedToConnect(device, reason))
            }
        }

        override fun onDeviceReady(device: BluetoothDevice) {
            logD(TAG, "onDeviceReady")
            loopWrite()
            isManualDisconnect = false
        }

        override fun onDeviceDisconnecting(device: BluetoothDevice) {
        }

        override fun onDeviceDisconnected(device: BluetoothDevice, reason: Int) {
            logD(TAG, "onDeviceDisconnected: $reason")
            cancelConnectWithTimeout()
            ModuleInject.messageManager.clearMessage()
            RiderBleManager.Companion.instance.release(isManualDisconnect)
            disconnectWifiConnection()
            mutableConnectionStatus.setState {
                copy(btStatus = BleStatus.DeviceDisconnected(device, reason))
            }
            if (!isManualDisconnect) {
                startScan()
            }
        }

        override fun onScanResult(devices: List<BleDevice>) {
            serviceCallbacks.forEach {
                it.get()?.onScanResult(devices)
            }
        }

        override fun onRequestBt() {
            serviceCallbacks.forEach {
                it.get()?.onRequestOpenBluetooth()
            }
        }

        override fun onScanning() {
            serviceCallbacks.forEach {
                it.get()?.onScanning()
            }
        }

        override fun onScanFinish() {
            serviceCallbacks.forEach {
                it.get()?.onScanFinish()
            }
        }

        override fun onNeedBluetoothScanPermission() {
            serviceCallbacks.forEach {
                it.get()?.onNeedBluetoothScanPermission()
            }
        }

        override fun onNeedLocationPermission() {
            serviceCallbacks.forEach {
                it.get()?.onNeedLocationPermission()
            }
        }

        override fun onEnableNotificationFailed(device: BluetoothDevice, status: Int) {
            serviceCallbacks.forEach {
                it.get()?.onEnableNotificationFailed(device, status)
            }
            disconnect()
        }
    }

    private val displayNaviCallback = object : DisplayNaviCallback {
        override fun onDeviceConnected() {
            logD(TAG, "Device connected (TCP/Transport layer established)")
            ConnectionTimeTracker.recordTcpConnected()

            val currentMode = RiderService.Companion.instance.getWifiConnectionMode()
            val configPrefs = ConfigPreferences.Companion.getInstance(application)

            if (currentMode == WifiConnectionMode.WIFI_AP_CLIENT) {
                if (candidateApSsid != null && candidateApPassword != null) {
                    logD(
                        TAG,
                        "AP Mode fully connected. Saving credentials: SSID=$candidateApSsid, BLE Name=$bleName"
                    )
                    logTcpConnection("TCP/Transport layer established", null, null)
                    configPrefs.setApSsid(candidateApSsid!!)
                    configPrefs.setApPassword(candidateApPassword!!)
                    bleName?.let { configPrefs.setBleName(it) }
                    bleAddress?.let { configPrefs.setBleAddress(it) }
                } else {
                    logW(TAG, "AP Mode connected, but no candidate AP credentials to save.")
                }
            } else if (currentMode == WifiConnectionMode.WIFI_P2P) {
                logD(TAG, "P2P Mode fully connected. Saving BLE credentials if available.")
                bleName?.let { configPrefs.setBleName(it) }
                bleAddress?.let { configPrefs.setBleAddress(it) }
            }
            candidateApSsid = null
            candidateApPassword = null

            logD(TAG, "wifi socket connected")
            displayNaviManager.start()
            mutableConnectionStatus.setState { copy(wifiStatus = WifiStatus.DeviceConnected) }
        }

        override fun onDeviceDisconnected() {
            logD(TAG, "wifi socket disconnected")
            mutableConnectionStatus.setState { copy(wifiStatus = WifiStatus.DeviceDisconnected) }
            requestWifiInfo()
        }

        override fun onDisplayInitialized(display: Display) {
            serviceCallbacks.forEach {
                it.get()?.onDisplayInitialized(display)
            }
        }

        override fun onDisplayReleased(display: Display) {
            serviceCallbacks.forEach {
                it.get()?.onDisplayReleased(display)
            }
        }

        override fun onVideoChannelReady() {
            serviceCallbacks.forEach {
                it.get()?.onVideoChannelReady()
            }
        }

        override fun onRequestMediaProjection() {
            serviceCallbacks.forEach {
                it.get()?.onRequestMediaProjection()
            }
        }

        override fun onMirrorStart() {
            serviceCallbacks.forEach {
                it.get()?.onMirrorStart()
            }
        }

        override fun onMirrorStop() {
            serviceCallbacks.forEach {
                it.get()?.onMirrorStop()
            }
        }

        override fun onTcpConnectionFailed(reason: Int) {
            logE(TAG, "TCP connection failed: $reason")
            // 触发TCP连接恢复机制
            val errorType = mapTcpErrorToRecoveryType(reason)
            connectionRecoveryManager.handleConnectionError(
                errorType,
                application,
                this@ConnectionManager
            )
        }

    }

    private fun loopWrite() {
        mainScope.launch(Dispatchers.IO) {
            while (RiderBleManager.Companion.instance.isConnected()) {
                ModuleInject.messageManager.dequeueOutgoing()?.let {
                    RiderBleManager.Companion.instance.write(it.toByteArray())
                }
            }
            RiderBleManager.Companion.instance.release(false)
        }
    }

    fun showDialog(title: String, message: String) {
        serviceCallbacks.forEach {
            it.get()?.onDialogShow(title, message)
        }
    }

    fun release() {
        disconnect()
        mutableConnectionStatus.setState {
            copy(
                wifiStatus = WifiStatus.IDLE,
                btStatus = BleStatus.IDLE
            )
        }
        displayNaviManager.removeCallback(displayNaviCallback)
        RiderBleManager.Companion.instance.release()
    }

    /**
     * 连接 BLE 设备
     */
    fun connectBle(device: BleDevice, context: Context) {
        RiderBleManager.Companion.instance.connect(device, context)
    }

    /**
     * 断开连接
     */
    @Synchronized
    fun disconnect(isManual: Boolean = true) {
        isManualDisconnect = isManual
        ModuleInject.messageManager.clearMessage()
        RiderBleManager.instance.release()
        displayNaviManager.sendByeByeRequest()
        displayNaviManager.release()

        // 如果是手动断开，同时断开WiFi连接
        if (isManual) {
            disconnectWifiConnection()
        }
    }

    /**
     * 断开WiFi连接（根据当前连接模式）
     */
    private fun disconnectWifiConnection() {
        logD(TAG, "Disconnecting WiFi connection (manual disconnect)")

        val currentMode = RiderService.Companion.instance.getWifiConnectionMode()
        when (currentMode) {
            WifiConnectionMode.WIFI_AP_CLIENT -> {
                wifiClientManager.disconnect()
                logD(TAG, "WiFi AP client disconnected")
            }

            WifiConnectionMode.WIFI_P2P -> {
                displayNaviManager.shutdown()
                logD(TAG, "WiFi P2P connection disconnected")
            }
        }
    }

    fun closeConnect() {
        ModuleInject.messageManager.clearMessage()
        RiderBleManager.Companion.instance.closeConnect()
    }

    /**
     * 搜索并连接 WLAN 直连
     * @param address 平台 MAC 地址
     * @param port 端口 默认 30512
     */
    fun startSearchWifiAndConnect(context: Context, address: String, port: Int) {
        if (mutableConnectionStatus.value.wifiStatus is WifiStatus.DeviceConnected) {
            logD(TAG, "wifi already connected")
            return
        }
        ConnectionTimeTracker.recordWifiConnecting("P2P")

        displayNaviManager.startSearchWifiAndConnect(
            context,
            bleName.toString(),
            bleAddress.toString(),
            address,
            port
        )
    }

    /**
     * Autolink启动成功
     */
    fun startAutolinkServiceSuccess() {
        displayNaviManager.setRequestNotificationSuccess()
    }

    /**
     * 开始投屏
     * @return 是否成功
     */
    fun startScreenProjection(): Boolean {
        return displayNaviManager.startScreenProjection()
    }

    /**
     * 停止投屏
     * @return 是否成功
     */
    fun stopScreenProjection(): Boolean {
        return displayNaviManager.stopScreenProjection()
    }

    /**
     * 发送手机屏幕信息给平台
     * @param isLandscape 是否横屏
     * @param rotation 屏幕旋转角度
     */
    fun sendOrientation(isLandscape: Int, rotation: Int = Platform.NORMAL) {
        displayNaviManager.sendOrientation(isLandscape, rotation)
    }

    /**
     * 请求平台 Wifi 信息
     * @param isReset 是否重置平台 wifi
     */
    fun requestWifiInfo(isReset: Boolean = true) {
        logD(TAG, "requestWifiInfo ${mutableConnectionStatus.value.btStatus}")
        if (mutableConnectionStatus.value.btStatus is BleStatus.DeviceConnected) {
            logConnectionD("WiFi info request")
            val currentMode = RiderService.Companion.instance.getWifiConnectionMode()
            val wifiMode = when (currentMode) {
                WifiConnectionMode.WIFI_AP_CLIENT -> RiderProtocol.WifiMode.WIFI_AP
                WifiConnectionMode.WIFI_P2P -> RiderProtocol.WifiMode.WIFI_P2P
            }

            logD(TAG, "Requesting WiFi info for mode: $currentMode")
            ModuleInject.messageManager.queueOutgoing(
                RiderProtocol.NaviMessageId.MSG_WIFI_INFO_REQUEST_VALUE,
                RiderProtocol.WifiInfoRequest
                    .newBuilder()
                    .setWifiMode(wifiMode)
                    .setIsResetWifi(isReset).build()
            )
        }
    }

    // WiFi客户端管理器（懒加载）
    private val wifiClientManager by lazy {
        WiFiClientManager(wifiClientListener)
    }

    // 连接恢复管理器
    private val connectionRecoveryManager = ConnectionRecoveryManager()

    // 连接恢复监听器
    private val recoveryListener = object : ConnectionRecoveryManager.ErrorHandlerListener {
        override fun onRecoveryStarted(errorType: ConnectionRecoveryManager.ErrorType) {
            logI(TAG, "Connection recovery started for error: $errorType")
        }

        override fun onRecoveryProgress(attempt: Int, maxAttempts: Int) {
            logI(TAG, "Recovery progress: $attempt/$maxAttempts")
        }

        override fun onRecoverySuccess(strategy: ConnectionRecoveryManager.RecoveryStrategy) {
            logI(TAG, "Connection recovery succeeded with strategy: $strategy")
        }

        override fun onRecoveryFailed(
            errorType: ConnectionRecoveryManager.ErrorType,
            finalStrategy: ConnectionRecoveryManager.RecoveryStrategy
        ) {
            logE(
                TAG,
                "Connection recovery failed for error: $errorType, final strategy: $finalStrategy"
            )
        }

        override fun onUserGuidanceRequired(
            errorType: ConnectionRecoveryManager.ErrorType,
            guidance: String
        ) {
            logW(TAG, "User guidance required for error: $errorType, guidance: $guidance")
            serviceCallbacks.forEach {
                it.get()?.onDialogShow("需要用户操作", guidance)
            }
        }
    }

    // WiFi客户端监听器
    private val wifiClientListener = object : WiFiClientManagerListener {
        override fun onWifiConnecting(ssid: String) {
            logD(TAG, "WiFi connecting to: $ssid")

            // 记录WiFi连接开始（AP客户端模式）
            ConnectionTimeTracker.recordWifiConnecting("AP_CLIENT")
        }

        override fun onWifiConnected(ssid: String, ipAddress: String) {
            logD(TAG, "WiFi connected: $ssid, IP: $ipAddress")
            updateWifiStatus(WifiStatus.DeviceConnected)

            // 记录WiFi连接成功
            ConnectionTimeTracker.recordWifiConnected()

            // 记录TCP连接开始
            ConnectionTimeTracker.recordTcpConnecting()

            // 启动TCP服务器连接
            displayNaviManager.startTcpServerConnection()
        }

        override fun onWifiDisconnected() {
            logD(TAG, "WiFi disconnected")
            updateWifiStatus(WifiStatus.DeviceDisconnected)
            displayNaviManager.shutdown()
        }

        override fun onWifiConnectionFailed(reason: Int) {
            logE(TAG, "WiFi connection failed: $reason")
            updateWifiStatus(WifiStatus.DeviceDisconnected)

            // 触发连接恢复机制
            val errorType = mapWifiErrorToRecoveryType(reason)
            connectionRecoveryManager.handleConnectionError(
                errorType,
                application,
                this@ConnectionManager
            )
        }

        override fun onNetworkScanComplete(networks: List<ScanResult>) {
            logD(TAG, "Network scan completed, found ${networks.size} networks")
        }

        override fun requestWifiInfo() {
            // 请求WiFi信息
            requestWifiInfo()
        }

        override fun onWifiState(opened: Boolean) {
            logD(TAG, "WiFi state changed: $opened")
        }
    }

    /**
     * 连接到仪表端WiFi热点（AP客户端模式）
     * @param context 应用上下文
     * @param ssid 仪表端WiFi热点名称
     * @param password 热点密码
     */
    fun connectToInstrumentWifi(context: Context, ssid: String, password: String) {
        logD(TAG, "Connecting to Instrument Wi-Fi: SSID=$ssid")
        logWifiConnection(
            "Start connecting to instrument Wi-Fi",
            ssid,
            "AP Mode"
        )

        if (mutableConnectionStatus.value.wifiStatus is WifiStatus.DeviceConnected) {
            logD(TAG, "WiFi already connected")
            return
        }

        // Set candidate credentials for saving on full success
        this.candidateApSsid = ssid
        this.candidateApPassword = password

        // Using the internal wifiClientManager to connect
        wifiClientManager.connectToWifi(ssid, password)
    }

    fun setNaviMode(naviMode: NaviMode) {
        displayNaviManager.setNaviMode(naviMode)
    }

    fun setMediaProjection(mediaProjection: MediaProjection) {
        displayNaviManager.setMediaProjection(mediaProjection)
    }

    private fun requestProtocolVersion() {
        if (mutableConnectionStatus.value.btStatus is BleStatus.DeviceConnected) {
            ModuleInject.messageManager.queueOutgoing(
                RiderProtocol.NaviMessageId.MSG_PROTOCOL_VERSION_REQUEST_VALUE,
                RiderProtocol.ProtocolVerRequest.newBuilder().build()
            )
            connectWithTimeout()
        }
    }

    private fun connectWithTimeout() {
        if (connectionTimer == null) {
            connectionTimer = Timer()
        }
        connectionTimer?.schedule(object : TimerTask() {
            override fun run() {
                protocolError(application.getString(R.string.connect_timeout))
            }
        }, 3000)
    }

    private fun cancelConnectWithTimeout() {
        connectionTimer?.cancel()
        connectionTimer?.purge()
        connectionTimer = null
    }

    private fun protocolError(message: String, error: Int = 0) {
        logE(TAG, message)
        showDialog(application.getString(R.string.error_title), message)
        if (error == NETWORK_ERROR) {
            disconnect(false)
        } else {
            disconnect()
        }
    }

    /**
     * BLE 设备是否连接
     * @return 是否连接
     */
    fun isBleConnected(): Boolean {
        return RiderBleManager.Companion.instance.isConnected()
    }

    /**
     * 匹配版本
     * @param version 版本
     */
    fun checkVersion(version: RiderProtocol.ProtocolVerNotification, isManualConnected: Boolean) {
        cancelConnectWithTimeout()
        if (version.major <= RiderProtocol.ProtocolVersion.MAJOR_VERSION_VALUE) {
            if (RiderBleManager.instance.isConfigConnect() && !isManualConnected) {
                val currentMode = RiderService.instance.getWifiConnectionMode()
                val configPreferences = ConfigPreferences.getInstance(application)

                if (currentMode == WifiConnectionMode.WIFI_AP_CLIENT) {
                    requestWifiInfo()
                } else { // WIFI_P2P mode (existing logic)
                    val address = configPreferences.getWifiAddress()
                    val port = configPreferences.getWifiPort()
                    if (address != null && port != 0) {
                        if (mutableConnectionStatus.value.wifiStatus is WifiStatus.DeviceConnected) {
                            logD(TAG, "P2P Mode: WiFi already connected (auto-connect)")
                            return
                        }
                        logD(
                            TAG,
                            "P2P Mode: Attempting auto-connect with saved P2P info, Address: $address"
                        )
                        // Assuming startSearchWifiAndConnect is for P2P
                        displayNaviManager.startSearchWifiAndConnect(
                            application,
                            bleName.toString(), // bleName and bleAddress might need re-evaluation for P2P context here
                            bleAddress.toString(),
                            address,
                            port
                        )
                    } else {
                        logD(TAG, "P2P Mode: No saved P2P info for auto-connect, requesting info.")
                        requestWifiInfo(false) // Request info if no saved P2P credentials
                    }
                }
            } else { // Auto-connect disabled or manual click
                logD(TAG, "Auto-connect disabled or manual BLE item click, requesting WiFi info.")
                requestWifiInfo(false)
            }
        } else {
            protocolError(application.getString(R.string.version_error))
        }
    }

    /**
     * 请求服务器激活
     * @param sdkKey 平台 SDK 的 key
     * @param macAddr 平台的MAC地址
     * @param timestamp 时间戳
     * @param sign 平台签名
     */
    fun requestActivate(
        sdkKey: String, macAddr: String, timestamp: String, sign: String
    ) {
        mainScope.launch(Dispatchers.IO) {
            val activeNotification = RiderProtocol.ActivateNotification.newBuilder()
            try {
                val result = authorizeRepository.requestActivateStatus(
                    key = sdkKey, macAddr, timestamp, sign
                )
                activeNotification.result = result.getOrThrow().status
                activeNotification.uuid = result.getOrThrow().uuid
            } catch (e: Exception) {
                activeNotification.result = NETWORK_ERROR
            }
            ModuleInject.messageManager.queueOutgoing(
                RiderProtocol.NaviMessageId.MSG_ACTIVE_NOTIFICATION_VALUE,
                activeNotification.build()
            )
            if (activeNotification.result != SUCCESS) {
                protocolError(application.getString(R.string.activate_failed))
            }
        }
    }

    /**
     * 请求服务器验证
     * @param productKey 产品 Key
     * @param address 平台的MAC地址
     * @param uuid 产品唯一ID
     * @param timestamp 时间戳
     * @param licenseSign 证书签名
     * @param sign 平台签名
     */
    fun requestVerify(
        productKey: String,
        address: String,
        uuid: String,
        timestamp: String,
        licenseSign: String,
        sign: String
    ) {
        val complianceNotification = RiderProtocol.ComplianceNotification.newBuilder()
        mainScope.launch {
            flow {
                emit(
                    authorizeRepository.requestCheckStatus(
                        productKey, address, uuid, timestamp, licenseSign, sign
                    )
                )
            }
                .retry(3) {
                    if (it is Exception) {
                        delay(1000)
                        true
                    } else {
                        false
                    }
                }
                .catch {
                    complianceNotification.result = NETWORK_ERROR
                    logE(TAG, "requestVerify: $it")
                }.flowOn(Dispatchers.IO)
                .collect {
                    complianceNotification.result = it.getOrThrow().status
                }
            ModuleInject.messageManager.queueOutgoing(
                RiderProtocol.NaviMessageId.MSG_COMPLICANCE_NOTIFICATION_VALUE,
                complianceNotification.build()
            )
            if (complianceNotification.result != SUCCESS) {
                if (complianceNotification.result == NETWORK_ERROR) {
                    protocolError(application.getString(R.string.network_error), NETWORK_ERROR)
                } else {
                    protocolError(application.getString(R.string.verify_failed))
                }
            } else {
                requestProtocolVersion()
                serviceCallbacks.forEach {
                    it.get()?.onClusterReady()
                }
            }
        }
    }

    /**
     * 开启设备扫描
     */
    fun startScan(shouldAutoConnect: Boolean = true) {
        RiderBleManager.Companion.instance.startScan(shouldAutoConnect)
    }

    /**
     * 停止设置扫描
     */
    fun stopScan() {
        RiderBleManager.Companion.instance.stopScan()
    }

    /**
     * 初始化
     * @param context
     */
    fun init(context: Context) {
        initConnectionTimeTracker(context)
        RiderBleManager.Companion.instance.addCallback(bleCallback)
        RiderBleManager.Companion.instance.registerBroadcastReceivers(context)
        // 设置连接恢复监听器
        connectionRecoveryManager.setErrorHandlerListener(recoveryListener)
    }

    /**
     * 销毁并释放资源
     * @param context
     */
    fun destroy(context: Context) {
        RiderBleManager.Companion.instance.stopScan()
        RiderBleManager.Companion.instance.removeCallback(bleCallback)
        RiderBleManager.Companion.instance.unregisterBroadcastReceivers(context)

        // 停止连接恢复
        connectionRecoveryManager.stopRecovery()
    }

    fun getAutolinkVersion(): String {
        return displayNaviManager.getAutolinkVersion()
    }

    /**
     * 获取连接信息
     * @return 连接信息
     * @see Connection
     */
    fun getConnectStatus(): Connection {
        return mutableConnectionStatus.value
    }

    /**
     * 导航模式发改变
     * @param mode 模式
     * @see NaviMode
     */
    fun sendNaviModeChange(mode: NaviMode) {
        serviceCallbacks.forEach {
            it.get()?.onNaviModeChange(mode)
        }
    }

    fun sendNaviModeChangeResponse(naviMode: RiderProtocol.NaviMode, ready: Boolean) {
        serviceCallbacks.forEach {
            it.get()?.onNaviModeChangeResponse(getNaviMode(naviMode), ready)
        }
    }

    fun sendRiderServiceConfigChange(riderServiceConfig: RiderServiceConfig) {
        serviceCallbacks.forEach { callback ->
            callback.get()?.onConfigChange(riderServiceConfig)
        }
    }

    fun getCurrentConnectDevice(): BleDevice? {
        return RiderBleManager.Companion.instance.getCurrentConnectDevice()
    }

    fun updateWeatherInfo() {
        serviceCallbacks.forEach { callback ->
            callback.get()?.onRequestWeatherInfo()
        }
    }

    fun naviVersionResponse(version: String) {
        serviceCallbacks.forEach { callback ->
            callback.get()?.naviVersionResponse(version)
        }
    }

    fun sendNaviModeStartResponse(naviMode: RiderProtocol.NaviMode) {
        serviceCallbacks.forEach { callback ->
            callback.get()?.onNaviModeStartResponse(getNaviMode(naviMode))
        }
    }

    fun sendNaviModeStopResponse(naviMode: RiderProtocol.NaviMode) {
        serviceCallbacks.forEach { callback ->
            callback.get()?.onNaviModeStopResponse(getNaviMode(naviMode))
        }
    }

    private fun getNaviMode(naviMode: RiderProtocol.NaviMode): NaviMode {
        return when (naviMode) {
            RiderProtocol.NaviMode.DEFAULT_NAVI -> NaviMode.Default
            RiderProtocol.NaviMode.SIMPLE_NAVI -> NaviMode.SimpleNavi
            RiderProtocol.NaviMode.SCREEN_NAVI -> NaviMode.ScreenNavi
            RiderProtocol.NaviMode.MIRROR_NAVI -> NaviMode.MirrorNAVI
            RiderProtocol.NaviMode.CRUISE_NAVI -> NaviMode.CruiseNAVI
            RiderProtocol.NaviMode.LOCK_SCREEN_NAVI -> NaviMode.LockScreenNavi
            RiderProtocol.NaviMode.NO_NAVI -> NaviMode.NoNavi
        }
    }

    fun disconnectWifi() {
        //displayNaviManager.shutdown()
        disconnectWifiConnection()
    }

    /**
     * 更新WiFi连接状态
     */
    private fun updateWifiStatus(status: WifiStatus) {
        val currentStatus = mutableConnectionStatus.value
        val newStatus = currentStatus.copy(wifiStatus = status)
        mutableConnectionStatus.value = newStatus
        logD(TAG, "WiFi status updated to: $status")
    }

    fun changeMap(type: Int) {
        serviceCallbacks.forEach { callback ->
            logD(TAG, "changeMap: callback")
            callback.get()?.changeMap(type)
        }
    }

    fun requestLockScreenDisplay() {
        displayNaviManager.requestLockScreenDisplay()
    }

    /**
     * 手动重置WiFi连接状态
     */
    fun resetWifiConnectionState() {
        logD(TAG, "Resetting WiFi connection state")

        // 根据当前模式重置相应的WiFi状态
        val currentMode = RiderService.Companion.instance.getWifiConnectionMode()
        when (currentMode) {
            WifiConnectionMode.WIFI_AP_CLIENT -> {
                wifiClientManager.resetConnectionState()
            }

            WifiConnectionMode.WIFI_P2P -> {
                displayNaviManager.resetP2pState()
            }
        }

        // 重置连接状态
        updateWifiStatus(WifiStatus.IDLE)
    }

    /**
     * 获取当前WiFi连接状态信息
     */
    fun getWifiConnectionInfo(): String {
        val currentMode = RiderService.Companion.instance.getWifiConnectionMode()
        return when (currentMode) {
            WifiConnectionMode.WIFI_AP_CLIENT -> {
                val state = wifiClientManager.getCurrentState()
                val attempts = wifiClientManager.getReconnectionAttempts()
                val sessionId = wifiClientManager.getCurrentSessionId()
                "AP Mode - State: $state, Reconnection attempts: $attempts, Session: $sessionId"
            }

            WifiConnectionMode.WIFI_P2P -> {
                displayNaviManager.getP2pConnectionInfo()
            }
        }
    }

    /**
     * 获取连接分析统计信息
     */
    fun getConnectionAnalyticsInfo(): String {
        val activeConnections = ConnectionAnalytics.getActiveConnectionCount()
        val history = ConnectionAnalytics.getConnectionHistory(10)

        return buildString {
            appendLine("=== Connection Analytics ===")
            appendLine("Active connections: $activeConnections")
            appendLine("Recent connections (last 10):")

            history.forEach { metrics ->
                appendLine(
                    "- ${metrics.connectionType}: ${metrics.deviceId} " +
                            "(${metrics.status}, ${metrics.duration}ms)"
                )
            }
        }
    }

    /**
     * 获取连接时间信息
     */
    fun getConnectionTimeInfo(): String {
        return ConnectionTimeTracker.getCurrentConnectionInfo()
    }

    /**
     * 初始化连接时间跟踪器（仅Debug模式）
     */
    fun initConnectionTimeTracker(context: Context) {
        ConnectionTimeTracker.init(context)
    }

    /**
     * 清除连接时间记录（仅Debug模式）
     */
    fun clearConnectionTimeRecords() {
        ConnectionTimeTracker.clearRecords()
    }


    /**
     * 将WiFi错误代码映射到恢复错误类型
     */
    private fun mapWifiErrorToRecoveryType(reason: Int): ConnectionRecoveryManager.ErrorType {
        return when (reason) {
            -1 -> ConnectionRecoveryManager.ErrorType.NETWORK_UNAVAILABLE
            -2 -> ConnectionRecoveryManager.ErrorType.AUTHENTICATION_FAILED
            -3 -> ConnectionRecoveryManager.ErrorType.CONNECTION_TIMEOUT
            -4 -> ConnectionRecoveryManager.ErrorType.WIFI_DISABLED
            -5 -> ConnectionRecoveryManager.ErrorType.PERMISSION_ERROR
            -6 -> ConnectionRecoveryManager.ErrorType.IP_ACQUISITION_FAILED
            else -> ConnectionRecoveryManager.ErrorType.UNKNOWN_ERROR
        }
    }

    /**
     * 将TCP错误代码映射到恢复错误类型
     */
    private fun mapTcpErrorToRecoveryType(reason: Int): ConnectionRecoveryManager.ErrorType {
        return when (reason) {
            -7 -> ConnectionRecoveryManager.ErrorType.TCP_CONNECTION_FAILED
            -8 -> ConnectionRecoveryManager.ErrorType.CONNECTION_TIMEOUT
            -9 -> ConnectionRecoveryManager.ErrorType.NETWORK_UNAVAILABLE
            else -> ConnectionRecoveryManager.ErrorType.TCP_CONNECTION_FAILED
        }
    }

    /**
     * 手动触发连接恢复
     */
    fun triggerConnectionRecovery(errorType: ConnectionRecoveryManager.ErrorType) {
        connectionRecoveryManager.handleConnectionError(errorType, application, this)
    }

    /**
     * 停止连接恢复
     */
    fun stopConnectionRecovery() {
        connectionRecoveryManager.stopRecovery()
    }

    /**
     * 获取连接恢复状态
     */
    fun isConnectionRecovering(): Boolean {
        return connectionRecoveryManager.isRecovering()
    }

    /**
     * 获取当前恢复尝试次数
     */
    fun getCurrentRecoveryAttempts(): Int {
        return connectionRecoveryManager.getCurrentRecoveryAttempts()
    }


    /**
     * 检查WiFi连接权限
     */
    fun checkWifiPermissions(): Boolean {
        val currentMode = RiderService.Companion.instance.getWifiConnectionMode()
        return when (currentMode) {
            WifiConnectionMode.WIFI_AP_CLIENT -> {
                wifiClientManager.checkPermissions()
            }

            WifiConnectionMode.WIFI_P2P -> {
                // P2P模式的权限检查逻辑
                true // 暂时返回true，实际应该检查P2P权限
            }
        }
    }

    /**
     * 检查WiFi是否启用
     */
    fun isWifiEnabled(): Boolean {
        val currentMode = RiderService.Companion.instance.getWifiConnectionMode()
        return when (currentMode) {
            WifiConnectionMode.WIFI_AP_CLIENT -> {
                wifiClientManager.isWifiEnabled()
            }

            WifiConnectionMode.WIFI_P2P -> {
                // P2P模式的WiFi检查逻辑
                true // 暂时返回true，实际应该检查P2P WiFi状态
            }
        }
    }

    /**
     * Called by RiderService to set potential AP credentials received from the instrument.
     * These will be saved permanently if the full connection (including TCP) succeeds.
     */
    internal fun setPotentialApCredentials(ssid: String, password: String) {
        logD(TAG, "Setting potential AP credentials: SSID=$ssid")
        this.tempApSsid = ssid
        this.tempApPassword = password
    }

    init {
        connectionStatus.collectWithScope(mainScope) { connection ->
            serviceCallbacks.forEach {
                it.get()?.onConnectStatusChange(connection)
            }
        }
        displayNaviManager.addCallback(displayNaviCallback)
    }


    companion object {
        private const val TAG = "ConnectionManager"
        private const val NETWORK_ERROR = -1
        private const val SUCCESS = 0
    }
}