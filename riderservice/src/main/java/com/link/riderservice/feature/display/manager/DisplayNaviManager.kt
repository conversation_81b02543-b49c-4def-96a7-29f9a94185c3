package com.link.riderservice.feature.display.manager

import android.content.Context
import android.media.projection.MediaProjection
import android.view.Display
import com.link.riderservice.api.RiderService
import com.link.riderservice.api.dto.AutoLinkConnect
import com.link.riderservice.api.dto.NaviMode
import com.link.riderservice.core.extensions.coroutines.mainScope
import com.link.riderservice.core.utils.countDownByFlow
import com.link.riderservice.core.utils.logging.logD
import com.link.riderservice.core.utils.logging.logE
import com.link.riderservice.core.utils.system.TimeUtils
import com.link.riderservice.feature.connection.transport.DataConnection
import com.link.riderservice.feature.connection.transport.Transport
import com.link.riderservice.feature.connection.transport.tcp.TcpConnection
import com.link.riderservice.feature.connection.transport.tcp.TcpServerConnection
import com.link.riderservice.feature.connection.wifi.SoftIP2pListener
import com.link.riderservice.feature.connection.wifi.SoftP2pManagerNew
import com.link.riderservice.feature.display.callback.DisplayNaviCallback
import com.link.riderservice.feature.display.protocol.project.Protos
import kotlinx.coroutines.Job
import java.io.IOException
import java.lang.ref.WeakReference
import java.net.Inet4Address
import java.net.InetAddress
import java.net.NetworkInterface
import java.util.Enumeration

/**
 * <AUTHOR>
 * @date 2022/8/25
 */
internal class DisplayNaviManager(
    requestWifiInfo: () -> Unit,
    onWifiState: (Boolean) -> Unit,
    private val onP2pWifiConnected: () -> Unit = {},
    private val onTcpConnecting: () -> Unit = {}
) : DataConnection.Callback {
    private var dataConnection: DataConnection? = null
    private var transport: Transport? = null
    private var countDown: Job? = null
    private val autolinkControl by lazy {
        AutolinkControl(RiderService.instance.getApplication())
    }
    private val p2pListener = object : SoftIP2pListener {
        override fun requestWifiInfo() {
            requestWifiInfo()
        }

        override fun onWifiState(opened: Boolean) {
            onWifiState(opened)
        }

        override fun onCancelConnect() {
            logD(TAG, "onCancelConnect")
            if (isP2pConnected) {
                shutdown()
            } else {
                requestWifiInfo()
            }
        }

        override fun onWifiConnectSuccess() {
            logD(TAG, "p2p connected for first time")
            logD("connect analysis:", "start autolink connect::${TimeUtils.getCurrentTimeStr()}")

            // 通知P2P WiFi连接成功
            onP2pWifiConnected()

            startConnectionEstablishing()
            isP2pConnected = true
            countDown?.cancel()
            val connect = AutoLinkConnect(getIp())
            RiderService.instance.sendMessageToRiderService(connect)
            waitForAutoLinkConnect(2)
        }

        override fun connectExist() {
            logD(TAG, "p2p already connected")

            // 通知P2P WiFi连接成功（已存在连接）
            onP2pWifiConnected()

            isP2pConnected = true
            startConnectionEstablishing()
            countDown?.cancel()
            val connect = AutoLinkConnect(getIp())
            RiderService.instance.sendMessageToRiderService(connect)
            waitForAutoLinkConnect(2)
        }

        override fun onP2pConnectSuccess() {
            logD(TAG, "onP2pConnectSuccess")
            logD("connect analysis:", "tcp connect start::${TimeUtils.getCurrentTimeStr()}")
            startConnectionEstablishing()
        }

        override fun onWifiDisconnect() {
            logD(TAG, "onWifiDisconnect")
            if (isP2pConnected) {
                shutdown()
            }
            isP2pConnected = false
        }
    }
    private val p2pManager by lazy { SoftP2pManagerNew(p2pListener) }
    private val displayNaviCallbacks: MutableList<WeakReference<DisplayNaviCallback>> = mutableListOf()
    private var mContext: Context? = null
    private var isRequestNotification = false
    private var isP2pConnected = false

    private val controlListener: AutolinkControl.OnControlListener =
        object : AutolinkControl.OnControlListener {
            override fun onDisplayInitialized(display: Display?) {
                displayNaviCallbacks.forEach {
                    display?.let { display ->
                        it.get()?.onDisplayInitialized(display)
                    }
                }
            }

            override fun onDisplayReleased(display: Display) {
                displayNaviCallbacks.forEach {
                    it.get()?.onDisplayReleased(display)
                }
            }

            @Deprecated("仪表盘不适用")
            override fun requestLandscape(isLandscape: Boolean) {

            }

            override fun onLoseConnected() {
                logD(TAG, "LoseConnected")
                shutdown()
            }

            override fun onUnrecoverableError(errorCode: Int) {
                logD(TAG, "Received unrecoverable error $errorCode. Shutting down.")
                shutdown()
            }

            override fun onDisconnected() {
                logD(TAG, "onDisconnected")
                shutdown()
            }

            override fun onByeByeRequest(reason: Int) {
                logD(TAG, "received ByeByeRequest with reason $reason")
                shutdown()
            }

            override fun onByeByeResponse() {
                logD(TAG, "received ByeByeResponse")
                shutdown()
            }

            override fun onStatusChanged(status: Int) {
                logD(TAG, "onStatusChanged: $status")
            }

            @Deprecated("仪表盘不适用")
            override fun onRequestAutoRotation(isAutoRotationEnabled: Boolean) {
            }

            override fun stopTransport() {
                transport?.stopTransport()
            }

            override fun onVideoChannelReady() {
                displayNaviCallbacks.forEach {
                    it.get()?.onVideoChannelReady()
                }
            }

            override fun onRequestMediaProjection() {
                displayNaviCallbacks.forEach {
                    it.get()?.onRequestMediaProjection()
                }
            }

            override fun onMirrorStart() {
                displayNaviCallbacks.forEach {
                    it.get()?.onMirrorStart()
                }
            }

            override fun onMirrorStop() {
                displayNaviCallbacks.forEach {
                    it.get()?.onMirrorStop()
                }
            }
        }

    /**
     * 增加回调
     * @param callback 回调
     * @see DisplayNaviCallback
     */
    @Synchronized
    fun addCallback(callback: DisplayNaviCallback) {
        displayNaviCallbacks.add(WeakReference(callback))
    }

    /**
     * 移除回调
     * @param callback 回调
     * @see DisplayNaviCallback
     */
    @Synchronized
    fun removeCallback(callback: DisplayNaviCallback) {
        displayNaviCallbacks.removeIf { it.get() == callback }
    }

    /**
     * 释放资源
     */
    @Synchronized
    fun release() {
        releaseTransport()
        releaseConnection()
        autolinkControl.release()
    }

    private fun releaseTransport() {
        isRequestNotification = false
        transport?.stopTransport()
        transport = null
    }

    private fun releaseConnection() {
        dataConnection?.shutdown()
        dataConnection = null
    }

    /**
     * 发送再见请求给平台
     */
    fun sendByeByeRequest() {
        if (transport?.isConnected() == true) {
            autolinkControl.sendByeByeRequest()
        }
    }

    /**
     * 开始投屏
     * @return 是否成功
     */
    fun startScreenProjection(): Boolean {
        return if (transport?.isConnected() == true) {
            autolinkControl.setEncoderState(Protos.VIDEO_FOCUS_PROJECTED, Protos.UNKNOWN)
            true
        } else {
            false
        }
    }

    /**
     * 停止投屏
     * @return 是否成功
     */
    fun stopScreenProjection(): Boolean {
        return if (transport?.isConnected() == true) {
            autolinkControl.setEncoderState(Protos.VIDEO_FOCUS_NATIVE, Protos.UNKNOWN)
            true
        } else {
            false
        }
    }

    /**
     * 发送手机屏幕信息给平台
     * @param isLandscape 是否横屏
     * @param rotation 屏幕旋转角度
     */
    fun sendOrientation(isLandscape: Int, rotation: Int) {
        transport?.takeIf { it.isConnected() }?.apply {
            autolinkControl.sendOrientation(isLandscape, rotation)
        }
    }

    /**
     * 搜索并连接 WLAN 直连
     * @param address 平台 MAC 地址
     * @param port 端口 默认 30512
     */
    fun startSearchWifiAndConnect(
        context: Context,
        nameBle: String,
        addressBle: String,
        address: String,
        port: Int = DEFAULT_PORT
    ) {
        val configPrefs = RiderService.instance.getConfigPreferences()
        //保存wifi键值对
        configPrefs.setBleName(nameBle)
        configPrefs.setBleAddress(addressBle)
        configPrefs.setWifiAddress(address)
        configPrefs.setWifiPort(port)

        logD(TAG, "connect address:$address port:$port")
        mContext = context
        p2pManager.start(address, port)
    }

    fun setNaviMode(naviMode: NaviMode) {
        autolinkControl.setNaviMode(naviMode)
    }

    fun setMediaProjection(mediaProjection: MediaProjection) {
        autolinkControl.setMediaProjection(mediaProjection)
    }


    private fun startConnectionEstablishing() {
        if (dataConnection == null) {
            dataConnection = TcpConnection()
        }

        // 通知TCP连接开始
        onTcpConnecting()

        try {
            dataConnection?.start(this)
        } catch (e: IOException) {
            logE(TAG, "startConnectionEstablishing failed", e)
        }
    }

    /**
     * 启动TCP服务器连接（用于AP客户端模式）
     * 在这种模式下，手机作为TCP服务器，仪表端作为TCP客户端连接
     */
    fun startTcpServerConnection() {
        logD(TAG, "Starting TCP server connection for AP client mode")

        // 确保先清理之前的连接
        if (dataConnection != null) {
            logD(TAG, "Cleaning up existing connection")
            dataConnection?.shutdown()
            dataConnection = null
        }

        // 创建专门的TCP服务器连接
        dataConnection = TcpServerConnection()

        try {
            dataConnection?.start(this)
            logD(TAG, "TCP server connection started successfully")
        } catch (e: IOException) {
            logE(TAG, "Failed to start TCP server connection", e)
            dataConnection = null
            // 通知TCP连接失败
            displayNaviCallbacks.forEach {
                it.get()?.onTcpConnectionFailed(-7) // TCP服务器启动失败
                it.get()?.onDeviceDisconnected()
            }
        }
    }


    /**
     * 开启与平台的交互
     */
    fun start() {
        startControl()
    }

    private fun startControl() {
        autolinkControl.registerListener(controlListener)
        autolinkControl.startConnect(transport)
    }

    override fun onConnected(transport: Transport) {
        logD(TAG, "socket connected")
        <EMAIL> = transport
        logD("connect analysis:", "tcp connect end::${TimeUtils.getCurrentTimeStr()}")
        displayNaviCallbacks.forEach {
            it.get()?.onDeviceConnected()
        }
    }

    /**
     * 重置P2P连接状态
     */
    fun resetP2pState() {
        logD(TAG, "Resetting P2P state")
        p2pManager.resetConnectionState()
    }

    /**
     * 获取P2P连接信息
     */
    fun getP2pConnectionInfo(): String {
        return p2pManager.getConnectionInfo()
    }

    @Synchronized
    fun shutdown() {
        releaseTransport()
        releaseConnection()
        p2pManager.stop()
        autolinkControl.release()
    }

    @Synchronized
    override fun onDisconnected() {
        logD(TAG, "Transport disconnected ${Thread.currentThread().name}")
        displayNaviCallbacks.forEach {
            it.get()?.onDeviceDisconnected()
        }
    }

    override fun requestLockScreenDisplay() {
        autolinkControl.requestLockScreenDisplay()
    }

    fun setRequestNotificationSuccess() {
        isRequestNotification = true
    }

    fun getAutolinkVersion(): String {
        return autolinkControl.getAutolinkVersion()
    }

    private fun waitForAutoLinkConnect(retryCount: Int) {
        countDown = countDownByFlow(
            10, 1000, mainScope,
            onTick = {
                logD(TAG, "次数：$retryCount,倒计时：$it,Autolink启动情况:$isRequestNotification")
                if (isRequestNotification || !isP2pConnected) {
                    countDown?.cancel()
                }
            }, onFinish = {
                if (!isRequestNotification) {
                    if (retryCount == 6) {
                        logD(TAG, "AutoLink 启动失败，屏蔽投屏导航")
                    } else {
                        logD(TAG, "send AutoLinkConnect")
                        val connect = AutoLinkConnect(getIp())
                        RiderService.instance.sendMessageToRiderService(connect)
                        waitForAutoLinkConnect(retryCount + 1)
                    }
                }
            })
    }

    fun getIp(): String {
        val networkInterfaces: Enumeration<NetworkInterface> = NetworkInterface.getNetworkInterfaces()
        while (networkInterfaces.hasMoreElements()) {
            val networkInterface: NetworkInterface = networkInterfaces.nextElement()
            val ipAddresses: Enumeration<InetAddress> = networkInterface.inetAddresses
            while (ipAddresses.hasMoreElements()) {
                val ipAddress: InetAddress = ipAddresses.nextElement()
                val ipAddressParts = ipAddress.hostAddress?.toString()?.split('.')
                if (!ipAddress.isLoopbackAddress && ipAddress is Inet4Address && ipAddressParts?.size == 4) {
                    if (networkInterface.name.contains("p2p")) {
                        return ipAddress.hostAddress?.toString() ?: ""
                    }
                }
            }
        }
        return ""
    }

    companion object {
        private const val TAG = "DisplayNaviManager"
        private const val DEFAULT_PORT = 30512
    }
}